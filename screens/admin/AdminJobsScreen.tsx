import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  Alert,
  RefreshControl,
  Modal,
  Image,
} from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import { Picker } from '@react-native-picker/picker'
import { supabase } from '../../lib/supabase'
import { useAuth } from '../../contexts/AuthContext'
import EditCustomerModal from '../../components/EditCustomerModal'
import EnhancedImage from '../../components/EnhancedImage'
import SimpleImage from '../../components/SimpleImage'

interface Job {
  id: string
  date: string
  customer_name: string
  customer_phone: string
  place_id?: string
  area_id?: string
  referral_source_id?: string
  place_name?: string
  area_name?: string
  referral_source_name?: string
  assigned_installer: string
  job_status: string
  completion_status: string
  images?: string[]
  latitude?: number
  longitude?: number
  is_pinned?: boolean
}

const AdminJobsScreen = () => {
  const [jobs, setJobs] = useState<Job[]>([])
  const [filteredJobs, setFilteredJobs] = useState<Job[]>([])
  const [loading, setLoading] = useState(true)
  const [searchText, setSearchText] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [completionFilter, setCompletionFilter] = useState('all')
  const [yearFilter, setYearFilter] = useState('all')
  const [monthFilter, setMonthFilter] = useState('all')
  const [selectedImage, setSelectedImage] = useState<string | null>(null)
  const [editingJob, setEditingJob] = useState<Job | null>(null)
  const [showEditModal, setShowEditModal] = useState(false)
  const [pinningJobId, setPinningJobId] = useState<string | null>(null)
  const { user, logout } = useAuth()

  useEffect(() => {
    fetchJobs()
  }, [])

  useEffect(() => {
    filterJobs()
  }, [jobs, searchText, statusFilter, completionFilter, yearFilter, monthFilter])

  const fetchJobs = async () => {
    try {
      const { data, error } = await supabase
        .from('jobs')
        .select(`
          *,
          places(name),
          areas(name),
          referral_sources(name)
        `)
        .order('created_at', { ascending: false })

      if (error) throw error

      const formattedJobs = data?.map(job => ({
        ...job,
        place_id: job.place_id,
        area_id: job.area_id,
        referral_source_id: job.referral_source_id,
        place_name: job.places?.name,
        area_name: job.areas?.name,
        referral_source_name: job.referral_sources?.name,
      })) || []

      setJobs(formattedJobs)
    } catch (error) {
      console.error('Error fetching jobs:', error)
      Alert.alert('هەڵە', 'نەتوانرا کارەکان بهێنرێنەوە')
    } finally {
      setLoading(false)
    }
  }

  const filterJobs = () => {
    let filtered = jobs

    // Search filter
    if (searchText) {
      filtered = filtered.filter(job =>
        job.customer_name.toLowerCase().includes(searchText.toLowerCase()) ||
        job.customer_phone.includes(searchText)
      )
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(job => job.job_status === statusFilter)
    }

    // Completion status filter
    if (completionFilter !== 'all') {
      filtered = filtered.filter(job => job.completion_status === completionFilter)
    }

    // Year filter
    if (yearFilter !== 'all') {
      filtered = filtered.filter(job =>
        new Date(job.date).getFullYear().toString() === yearFilter
      )
    }

    // Month filter
    if (monthFilter !== 'all') {
      filtered = filtered.filter(job =>
        (new Date(job.date).getMonth() + 1).toString() === monthFilter
      )
    }

    // Sort by pinned status first, then by creation date
    filtered.sort((a, b) => {
      // Pinned items first
      if (a.is_pinned && !b.is_pinned) return -1
      if (!a.is_pinned && b.is_pinned) return 1

      // Within same pin status, sort by creation date (newest first)
      return new Date(b.created_at || b.date).getTime() - new Date(a.created_at || a.date).getTime()
    })

    setFilteredJobs(filtered)
  }

  const updateJobStatus = async (jobId: string, newStatus: string) => {
    try {
      const { error } = await supabase
        .from('jobs')
        .update({
          job_status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', jobId)

      if (error) throw error

      // Provide appropriate feedback based on status change
      if (newStatus === 'ready') {
        Alert.alert('سەرکەوتوو', 'کارەکە ئامادە کرا و نێردرا بۆ وەستا')
      } else if (newStatus === 'not_ready') {
        Alert.alert('سەرکەوتوو', 'کارەکە وەک حازر نییە نیشان کرا')
      }

      fetchJobs()
    } catch (error) {
      console.error('Error updating job status:', error)
      Alert.alert('هەڵە', 'نەتوانرا دۆخی کارەکە نوێ بکرێتەوە')
    }
  }

  const sendToDispatch = async (jobId: string) => {
    try {
      const { error } = await supabase
        .from('jobs')
        .update({ 
          assigned_to_dispatch: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', jobId)

      if (error) throw error

      Alert.alert('سەرکەوتوو', 'کارەکە نێردرا بۆ ناردن')
      fetchJobs()
    } catch (error) {
      console.error('Error sending to dispatch:', error)
      Alert.alert('هەڵە', 'نەتوانرا کارەکە بنێردرێت')
    }
  }

  const togglePinJob = async (jobId: string, currentPinStatus: boolean) => {
    setPinningJobId(jobId)

    try {
      const { error } = await supabase
        .from('jobs')
        .update({
          is_pinned: !currentPinStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', jobId)

      if (error) throw error

      const message = !currentPinStatus ? 'کارەکە سەنجاق کرا' : 'سەنجاقی کارەکە لابرا'
      Alert.alert('سەرکەوتوو', message)
      fetchJobs()
    } catch (error) {
      console.error('Error toggling pin status:', error)
      Alert.alert('هەڵە', 'نەتوانرا دۆخی سەنجاق گۆڕدرێت')
    } finally {
      setPinningJobId(null)
    }
  }

  const deleteJob = async (jobId: string) => {
    Alert.alert(
      'سڕینەوە',
      'دڵنیایت لە سڕینەوەی ئەم کارە؟',
      [
        { text: 'نەخێر', style: 'cancel' },
        {
          text: 'بەڵێ',
          style: 'destructive',
          onPress: async () => {
            try {
              const { error } = await supabase
                .from('jobs')
                .delete()
                .eq('id', jobId)

              if (error) throw error

              Alert.alert('سەرکەوتوو', 'کارەکە سڕایەوە')
              fetchJobs()
            } catch (error) {
              console.error('Error deleting job:', error)
              Alert.alert('هەڵە', 'نەتوانرا کارەکە بسڕێتەوە')
            }
          }
        }
      ]
    )
  }

  const openEditCustomer = (job: Job) => {
    setEditingJob(job)
    setShowEditModal(true)
  }

  const closeEditModal = () => {
    setShowEditModal(false)
    setEditingJob(null)
  }

  const handleEditSave = () => {
    fetchJobs() // Refresh the jobs list after editing
  }

  const renderJobCard = ({ item }: { item: Job }) => (
    <View style={[
      styles.jobCard,
      item.is_pinned && styles.pinnedCard
    ]}>
      {/* Header Section */}
      <View style={styles.cardHeader}>
        <View style={styles.headerLeft}>
          <View style={styles.customerNameContainer}>
            <Ionicons name="person-circle" size={24} color="#3B82F6" />
            <Text style={styles.customerName}>{item.customer_name}</Text>
          </View>
        </View>

        <View style={styles.headerRight}>
          {/* Pin indicator */}
          {item.is_pinned && (
            <View style={styles.pinIndicator}>
              <Ionicons name="push" size={16} color="#FF6B35" />
            </View>
          )}

          {/* Status Badge */}
          {item.completion_status === 'completed' ? (
            <View style={[styles.statusBadge, styles.completedBadge]}>
              <Ionicons name="checkmark-circle" size={16} color="white" />
              <Text style={styles.statusText}>تەواو بووە</Text>
            </View>
          ) : (
            <View style={[
              styles.statusBadge,
              item.job_status === 'ready' ? styles.readyBadge : styles.notReadyBadge
            ]}>
              <Ionicons
                name={item.job_status === 'ready' ? 'checkmark-circle' : 'time'}
                size={16}
                color="white"
              />
              <Text style={styles.statusText}>
                {item.job_status === 'ready' ? 'حازرە' : 'حازر نییە'}
              </Text>
            </View>
          )}

          {/* Not Completed Badge */}
          {item.job_status === 'ready' && item.completion_status === 'not_completed' && (
            <View style={[styles.statusBadge, styles.notCompletedBadge, styles.secondaryBadge]}>
              <Ionicons name="close-circle" size={14} color="white" />
              <Text style={styles.statusTextSmall}>تەواو نەبوو</Text>
            </View>
          )}
        </View>
      </View>

      {/* Contact and Info Section */}
      <View style={styles.infoSection}>
        {/* Phone */}
        <View style={styles.infoItem}>
          <View style={styles.infoIconContainer}>
            <Ionicons name="call" size={18} color="#3B82F6" />
          </View>
          <Text style={styles.infoText}>{item.customer_phone}</Text>
        </View>

        {/* Location */}
        <View style={styles.infoItem}>
          <View style={styles.infoIconContainer}>
            <Ionicons name="location" size={18} color="#10B981" />
          </View>
          <Text style={styles.infoText}>
            {item.place_name} - {item.area_name}
          </Text>
        </View>

        {/* Referral Source */}
        {item.referral_source_name && (
          <View style={styles.infoItem}>
            <View style={styles.infoIconContainer}>
              <Ionicons name="people" size={18} color="#8B5CF6" />
            </View>
            <Text style={styles.infoText}>
              لە لایەن: {item.referral_source_name}
            </Text>
          </View>
        )}

        {/* Date */}
        <View style={styles.infoItem}>
          <View style={styles.infoIconContainer}>
            <Ionicons name="calendar" size={18} color="#6B7280" />
          </View>
          <Text style={styles.infoTextSecondary}>
            {new Date(item.date).toLocaleDateString('ku')}
          </Text>
        </View>
      </View>

      {/* Images section */}
      {item.images && item.images.length > 0 && (
        <View style={styles.imagesContainer}>
          <View style={styles.imagesHeader}>
            <Ionicons name="images" size={16} color="#6C757D" />
            <Text style={styles.imagesLabel}>{item.images.length} وێنە</Text>
          </View>
          <Text style={styles.debugText}>Debug: {JSON.stringify(item.images.slice(0, 1))}</Text>
          <View style={styles.imagesList}>
            {item.images.slice(0, 3).map((image, index) => {
              console.log(`Rendering image ${index}:`, image)
              return (
                <SimpleImage
                  key={index}
                  source={{ uri: image }}
                  style={styles.thumbImage}
                  containerStyle={styles.imageThumb}
                  onPress={() => setSelectedImage(image)}
                />
              )
            })}
            {item.images.length > 3 && (
              <View style={styles.moreImagesIndicator}>
                <Text style={styles.moreImagesText}>+{item.images.length - 3}</Text>
              </View>
            )}
          </View>
        </View>
      )}

      {/* Action Buttons Section */}
      <View style={styles.actionsSection}>
        <View style={styles.actionButtonsContainer}>
          {/* Primary Action Buttons */}
          <View style={styles.primaryActionsRow}>
            {/* Toggle Ready/Not Ready Button */}
            {item.completion_status !== 'completed' && (
              <TouchableOpacity
                style={[
                  styles.primaryActionBtn,
                  item.job_status === 'ready' ? styles.warningBtn : styles.successBtn
                ]}
                onPress={() => updateJobStatus(
                  item.id,
                  item.job_status === 'ready' ? 'not_ready' : 'ready'
                )}
              >
                <Ionicons
                  name={item.job_status === 'ready' ? 'time' : 'checkmark-circle'}
                  size={16}
                  color="white"
                />
                <Text style={styles.primaryBtnText}>
                  {item.job_status === 'ready' ? 'حازر نییە' : 'حازرە'}
                </Text>
              </TouchableOpacity>
            )}

            {/* Dispatch Button */}
            <TouchableOpacity
              style={[styles.primaryActionBtn, styles.primaryBtn]}
              onPress={() => sendToDispatch(item.id)}
            >
              <Ionicons name="send" size={16} color="white" />
              <Text style={styles.primaryBtnText}>ناردن</Text>
            </TouchableOpacity>
          </View>

          {/* Secondary Action Buttons */}
          <View style={styles.secondaryActionsRow}>
            <TouchableOpacity
              style={[styles.secondaryActionBtn, styles.editBtn]}
              onPress={() => openEditCustomer(item)}
            >
              <Ionicons name="create-outline" size={16} color="#6B7280" />
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.secondaryActionBtn,
                styles.pinBtn,
                item.is_pinned && styles.pinnedBtn
              ]}
              onPress={() => togglePinJob(item.id, item.is_pinned || false)}
              disabled={pinningJobId === item.id}
            >
              <Ionicons
                name={pinningJobId === item.id ? "hourglass" : (item.is_pinned ? "push" : "push-outline")}
                size={16}
                color={item.is_pinned ? "#FF6B35" : "#6B7280"}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.secondaryActionBtn, styles.deleteBtn]}
              onPress={() => deleteJob(item.id)}
            >
              <Ionicons name="trash-outline" size={16} color="#EF4444" />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </View>
  )

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={logout} style={styles.logoutButton}>
          <Ionicons name="log-out-outline" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>ماڵەکان</Text>
        <Text style={styles.welcomeText}>بەخێربێیت {user?.display_name}</Text>
      </View>

      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="گەڕان بە ناو یان ژمارەی تەلەفۆن"
          value={searchText}
          onChangeText={setSearchText}
          textAlign="right"
        />
      </View>

      <View style={styles.filtersContainer}>
        <View style={styles.filterItem}>
          <Text style={styles.filterLabel}>دۆخ:</Text>
          <Picker
            selectedValue={statusFilter}
            onValueChange={setStatusFilter}
            style={styles.picker}
          >
            <Picker.Item label="هەموو" value="all" />
            <Picker.Item label="حازرە" value="ready" />
            <Picker.Item label="حازر نییە" value="not_ready" />
          </Picker>
        </View>

        <View style={styles.filterItem}>
          <Text style={styles.filterLabel}>تەواوکردن:</Text>
          <Picker
            selectedValue={completionFilter}
            onValueChange={setCompletionFilter}
            style={styles.picker}
          >
            <Picker.Item label="هەموو" value="all" />
            <Picker.Item label="تەواو بوو" value="completed" />
            <Picker.Item label="تەواو نەبوو" value="not_completed" />
            <Picker.Item label="چاوەڕوانە" value="pending" />
          </Picker>
        </View>
      </View>

      <FlatList
        data={filteredJobs}
        renderItem={renderJobCard}
        keyExtractor={(item) => item.id}
        refreshControl={
          <RefreshControl refreshing={loading} onRefresh={fetchJobs} />
        }
        contentContainerStyle={styles.listContainer}
      />

      <Modal
        visible={!!selectedImage}
        transparent={true}
        onRequestClose={() => setSelectedImage(null)}
      >
        <View style={styles.modalContainer}>
          <TouchableOpacity
            style={styles.modalCloseButton}
            onPress={() => setSelectedImage(null)}
          >
            <Ionicons name="close" size={30} color="white" />
          </TouchableOpacity>
          {selectedImage && (
            <EnhancedImage
              source={{ uri: selectedImage }}
              style={styles.fullImage}
              showRetry={true}
            />
          )}
        </View>
      </Modal>

      <EditCustomerModal
        visible={showEditModal}
        job={editingJob}
        onClose={closeEditModal}
        onSave={handleEditSave}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: 'white',
    padding: 16,
    paddingTop: 50,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  welcomeText: {
    fontSize: 14,
    color: '#666',
  },
  logoutButton: {
    padding: 8,
  },
  searchContainer: {
    padding: 16,
  },
  searchInput: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#ddd',
    textAlign: 'right',
  },
  filtersContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  filterItem: {
    flex: 1,
    marginRight: 8,
  },
  filterLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
    textAlign: 'right',
  },
  picker: {
    backgroundColor: 'white',
    borderRadius: 8,
  },
  listContainer: {
    padding: 16,
  },
  jobCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  pinnedCard: {
    backgroundColor: '#FFF7ED',
    borderColor: '#FF6B35',
    borderWidth: 1.5,
    shadowColor: '#FF6B35',
    shadowOpacity: 0.15,
  },
  // Header Section Styles
  cardHeader: {
    flexDirection: 'row-reverse',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  headerLeft: {
    flex: 1,
  },
  headerRight: {
    flexDirection: 'column',
    alignItems: 'flex-end',
    gap: 8,
  },
  customerNameContainer: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    gap: 8,
  },
  customerName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    textAlign: 'right',
    flex: 1,
  },
  pinIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#FFF7ED',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#FF6B35',
  },


  // Status Badge Styles
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 4,
  },
  readyBadge: {
    backgroundColor: '#10B981',
  },
  notReadyBadge: {
    backgroundColor: '#FACC15',
  },
  completedBadge: {
    backgroundColor: '#10B981',
  },
  notCompletedBadge: {
    backgroundColor: '#EF4444',
  },
  secondaryBadge: {
    marginTop: 4,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  statusTextSmall: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
  completionBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 16,
    marginTop: 6,
    marginRight: 8,
  },
  completedBadge: {
    backgroundColor: '#28a745',
  },
  notCompletedBadge: {
    backgroundColor: '#dc3545',
  },
  pendingBadge: {
    backgroundColor: '#6c757d',
  },
  completionBadgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
    marginLeft: 3,
  },
  // Info Section Styles
  infoSection: {
    marginBottom: 16,
    gap: 12,
  },
  infoItem: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    gap: 12,
  },
  infoIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 8,
    backgroundColor: '#F9FAFB',
    alignItems: 'center',
    justifyContent: 'center',
  },
  infoText: {
    fontSize: 15,
    color: '#374151',
    fontWeight: '500',
    textAlign: 'right',
    flex: 1,
  },
  infoTextSecondary: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '400',
    textAlign: 'right',
    flex: 1,
  },

  imagesContainer: {
    marginBottom: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 12,
  },
  imagesHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  imagesLabel: {
    fontSize: 14,
    color: '#6C757D',
    marginLeft: 6,
    fontWeight: '500',
  },
  debugText: {
    fontSize: 10,
    color: '#999',
    marginBottom: 8,
    fontFamily: 'monospace',
  },
  imagesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  imageThumb: {
    marginRight: 8,
    marginBottom: 8,
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  thumbImage: {
    width: 70,
    height: 70,
  },
  moreImagesIndicator: {
    width: 70,
    height: 70,
    borderRadius: 12,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  moreImagesText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  // Action Buttons Styles
  actionsSection: {
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
    paddingTop: 16,
    marginTop: 4,
  },
  actionButtonsContainer: {
    flexDirection: 'row-reverse',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 12,
  },

  // Primary Actions Row
  primaryActionsRow: {
    flexDirection: 'row-reverse',
    gap: 8,
    flex: 1,
  },
  primaryActionBtn: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 6,
    minWidth: 80,
    maxWidth: 100,
    flex: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  primaryBtn: {
    backgroundColor: '#3B82F6',
  },
  successBtn: {
    backgroundColor: '#10B981',
  },
  warningBtn: {
    backgroundColor: '#FACC15',
  },
  primaryBtnText: {
    color: 'white',
    fontSize: 13,
    fontWeight: '600',
    textAlign: 'center',
  },

  // Secondary Actions Row
  secondaryActionsRow: {
    flexDirection: 'row-reverse',
    gap: 6,
    alignItems: 'center',
  },
  secondaryActionBtn: {
    width: 36,
    height: 36,
    borderRadius: 8,
    backgroundColor: '#F9FAFB',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  editBtn: {
    backgroundColor: '#F9FAFB',
  },
  pinBtn: {
    backgroundColor: '#F9FAFB',
  },
  pinnedBtn: {
    backgroundColor: '#FFF7ED',
    borderColor: '#FF6B35',
  },
  deleteBtn: {
    backgroundColor: '#FEF2F2',
    borderColor: '#FECACA',
  },

  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalCloseButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 1,
  },
  fullImage: {
    width: '90%',
    height: '70%',
    resizeMode: 'contain',
  },
})

export default AdminJobsScreen
