import React, { useState } from 'react'
import { 
  Image, 
  View, 
  Text, 
  StyleSheet, 
  ActivityIndicator, 
  TouchableOpacity,
  ImageStyle,
  ViewStyle
} from 'react-native'
import { Ionicons } from '@expo/vector-icons'

interface SimpleImageProps {
  source: { uri: string }
  style?: ImageStyle
  containerStyle?: ViewStyle
  onPress?: () => void
}

const SimpleImage: React.FC<SimpleImageProps> = ({
  source,
  style,
  containerStyle,
  onPress
}) => {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(false)

  const handleLoadStart = () => {
    console.log('SimpleImage: Load started -', source.uri)
    setLoading(true)
    setError(false)
  }

  const handleLoad = () => {
    console.log('SimpleImage: Load success -', source.uri)
    setLoading(false)
    setError(false)
  }

  const handleError = (errorEvent: any) => {
    console.log('SimpleImage: Load error -', source.uri, errorEvent.nativeEvent?.error)
    setLoading(false)
    setError(true)
  }

  const renderContent = () => {
    if (loading && !error) {
      return (
        <View style={[styles.placeholderContainer, style]}>
          <ActivityIndicator size="small" color="#007AFF" />
          <Text style={styles.loadingText}>بارکردن...</Text>
        </View>
      )
    }

    if (error) {
      return (
        <View style={[styles.errorContainer, style]}>
          <Ionicons name="image-outline" size={24} color="#999" />
          <Text style={styles.errorText}>نەتوانرا وێنەکە بار بکرێت</Text>
        </View>
      )
    }

    return (
      <Image
        source={{ uri: source.uri }}
        style={style}
        onLoadStart={handleLoadStart}
        onLoad={handleLoad}
        onError={handleError}
        resizeMode="cover"
      />
    )
  }

  const content = renderContent()

  if (onPress) {
    return (
      <TouchableOpacity 
        style={[styles.container, containerStyle]} 
        onPress={onPress}
        disabled={loading || error}
      >
        {content}
      </TouchableOpacity>
    )
  }

  return (
    <View style={[styles.container, containerStyle]}>
      {content}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
  placeholderContainer: {
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  errorContainer: {
    backgroundColor: '#f8f8f8',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    borderWidth: 1,
    borderColor: '#ddd',
    borderStyle: 'dashed',
  },
  errorText: {
    marginTop: 8,
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
  },
})

export default SimpleImage
